import 'package:flutter/material.dart';
import 'dart:ui' as ui;
import 'package:google_fonts/google_fonts.dart';
import 'package:intl/intl.dart';
import '../models/reservation_response_model.dart';
import '../services/table_reservation_service.dart';

class ReservationsDrawer extends StatefulWidget {
  const ReservationsDrawer({super.key});

  @override
  State<ReservationsDrawer> createState() => _ReservationsDrawerState();
}

class _ReservationsDrawerState extends State<ReservationsDrawer> {
  ReservationsApiResponse? _reservationsData;
  bool _isLoading = true;
  String? _error;
  DateTime _selectedDate = DateTime.now();

  @override
  void initState() {
    super.initState();
    _fetchReservations();
  }

  Future<void> _fetchReservations() async {
    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      final response = await TableReservationService.getAllReservations(
        date: _selectedDate,
        includeMonth: true,
      );

      setState(() {
        _reservationsData = response;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _error = 'Failed to load reservations: $e';
        _isLoading = false;
      });
    }
  }

  Future<void> _selectDate() async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: _selectedDate,
      firstDate: DateTime.now().subtract(const Duration(days: 30)),
      lastDate: DateTime.now().add(const Duration(days: 90)),
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: const ColorScheme.dark(
              primary: Colors.blue,
              onPrimary: Colors.white,
              surface: Color(0xFF2A2A2A),
              onSurface: Colors.white,
            ),
          ),
          child: child!,
        );
      },
    );

    if (picked != null && picked != _selectedDate) {
      setState(() {
        _selectedDate = picked;
      });
      _fetchReservations();
    }
  }

  @override
  Widget build(BuildContext context) {
    return OrientationBuilder(
      builder: (context, orientation) {
        double drawerWidth = orientation == Orientation.portrait
            ? MediaQuery.of(context).size.width * 0.85
            : MediaQuery.of(context).size.width * 0.6;

        return BackdropFilter(
          filter: ui.ImageFilter.blur(sigmaX: 12, sigmaY: 12),
          child: Drawer(
            backgroundColor: Colors.transparent,
            width: drawerWidth,
            child: Container(
              decoration: BoxDecoration(
                color: Colors.black.withOpacity(0.8),
                border: Border(
                  right: BorderSide(color: Colors.grey.shade800),
                ),
              ),
              child: Column(
                children: [
                  _buildHeader(),
                  _buildDateSelector(),
                  const Divider(color: Colors.grey),
                  Expanded(child: _buildReservationsList()),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.fromLTRB(16, 50, 16, 16),
      child: Row(
        children: [
          Icon(
            Icons.event_note,
            color: Colors.blue.shade400,
            size: 28,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              'Reservations',
              style: GoogleFonts.poppins(
                color: Colors.white,
                fontSize: 24,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
          IconButton(
            icon: const Icon(Icons.close, color: Colors.white70),
            onPressed: () => Navigator.of(context).pop(),
          ),
        ],
      ),
    );
  }

  Widget _buildDateSelector() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Row(
        children: [
          Expanded(
            child: GestureDetector(
              onTap: _selectDate,
              child: Container(
                padding:
                    const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                decoration: BoxDecoration(
                  color: Colors.blue.shade600.withOpacity(0.2),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.blue.shade600),
                ),
                child: Row(
                  children: [
                    Icon(
                      Icons.calendar_today,
                      color: Colors.blue.shade400,
                      size: 20,
                    ),
                    const SizedBox(width: 8),
                    Text(
                      DateFormat('MMM dd, yyyy').format(_selectedDate),
                      style: GoogleFonts.poppins(
                        color: Colors.white,
                        fontSize: 16,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
          const SizedBox(width: 8),
          IconButton(
            icon: Icon(
              Icons.refresh,
              color: Colors.blue.shade400,
            ),
            onPressed: _fetchReservations,
          ),
        ],
      ),
    );
  }

  Widget _buildReservationsList() {
    if (_isLoading) {
      return const Center(
        child: CircularProgressIndicator(color: Colors.blue),
      );
    }

    if (_error != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              color: Colors.red.shade400,
              size: 48,
            ),
            const SizedBox(height: 16),
            Text(
              _error!,
              style: GoogleFonts.poppins(
                color: Colors.red.shade400,
                fontSize: 16,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: _fetchReservations,
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.blue.shade600,
              ),
              child: Text(
                'Retry',
                style: GoogleFonts.poppins(color: Colors.white),
              ),
            ),
          ],
        ),
      );
    }

    if (_reservationsData == null) {
      return Center(
        child: Text(
          'No data available',
          style: GoogleFonts.poppins(
            color: Colors.white70,
            fontSize: 16,
          ),
        ),
      );
    }

    final weeklyReservations =
        _reservationsData!.data.reservationsInCurrentWeek;
    final monthlyReservations = _reservationsData!.data.reservationsInMonth;

    if (weeklyReservations.isEmpty && monthlyReservations.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.event_busy,
              color: Colors.grey.shade400,
              size: 48,
            ),
            const SizedBox(height: 16),
            Text(
              'No reservations found',
              style: GoogleFonts.poppins(
                color: Colors.grey.shade400,
                fontSize: 18,
                fontWeight: FontWeight.w500,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'for ${DateFormat('MMM dd, yyyy').format(_selectedDate)}',
              style: GoogleFonts.poppins(
                color: Colors.grey.shade500,
                fontSize: 14,
              ),
            ),
          ],
        ),
      );
    }

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (weeklyReservations.isNotEmpty) ...[
            _buildSectionHeader('This Week (${weeklyReservations.length})'),
            const SizedBox(height: 8),
            ...weeklyReservations.map(_buildReservationCard),
            const SizedBox(height: 24),
          ],
          if (monthlyReservations.isNotEmpty) ...[
            _buildSectionHeader('This Month (${monthlyReservations.length})'),
            const SizedBox(height: 8),
            ...monthlyReservations.map(_buildReservationCard),
          ],
        ],
      ),
    );
  }

  Widget _buildSectionHeader(String title) {
    return Text(
      title,
      style: GoogleFonts.poppins(
        color: Colors.blue.shade400,
        fontSize: 18,
        fontWeight: FontWeight.w600,
      ),
    );
  }

  Widget _buildReservationCard(ReservationItem reservation) {
    final isToday =
        DateUtils.isSameDay(reservation.reservationTime, DateTime.now());
    final isPast = reservation.reservationTime.isBefore(DateTime.now());

    Color statusColor;
    IconData statusIcon;

    switch (reservation.status.toLowerCase()) {
      case 'confirmed':
        statusColor = Colors.green.shade400;
        statusIcon = Icons.check_circle;
        break;
      case 'arrived':
        statusColor = Colors.blue.shade400;
        statusIcon = Icons.person;
        break;
      case 'cancelled':
        statusColor = Colors.red.shade400;
        statusIcon = Icons.cancel;
        break;
      default:
        statusColor = Colors.orange.shade400;
        statusIcon = Icons.schedule;
    }

    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey.shade900.withOpacity(0.6),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: isToday ? Colors.blue.shade600 : Colors.grey.shade700,
          width: isToday ? 2 : 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Expanded(
                child: Text(
                  reservation.customerName,
                  style: GoogleFonts.poppins(
                    color: Colors.white,
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: statusColor.withOpacity(0.2),
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(color: statusColor),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(statusIcon, color: statusColor, size: 14),
                    const SizedBox(width: 4),
                    Text(
                      reservation.status.toUpperCase(),
                      style: GoogleFonts.poppins(
                        color: statusColor,
                        fontSize: 12,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Row(
            children: [
              Icon(Icons.access_time, color: Colors.grey.shade400, size: 16),
              const SizedBox(width: 6),
              Text(
                DateFormat('MMM dd, yyyy • hh:mm a')
                    .format(reservation.reservationTime),
                style: GoogleFonts.poppins(
                  color: isPast ? Colors.grey.shade500 : Colors.grey.shade300,
                  fontSize: 14,
                ),
              ),
            ],
          ),
          const SizedBox(height: 6),
          Row(
            children: [
              Icon(Icons.table_restaurant,
                  color: Colors.grey.shade400, size: 16),
              const SizedBox(width: 6),
              Text(
                'Table ${reservation.table.name}',
                style: GoogleFonts.poppins(
                  color: Colors.grey.shade300,
                  fontSize: 14,
                ),
              ),
              const SizedBox(width: 16),
              Icon(Icons.people, color: Colors.grey.shade400, size: 16),
              const SizedBox(width: 6),
              Text(
                '${reservation.numberOfGuests} guests',
                style: GoogleFonts.poppins(
                  color: Colors.grey.shade300,
                  fontSize: 14,
                ),
              ),
            ],
          ),
          if (reservation.phoneNumber.isNotEmpty) ...[
            const SizedBox(height: 6),
            Row(
              children: [
                Icon(Icons.phone, color: Colors.grey.shade400, size: 16),
                const SizedBox(width: 6),
                Text(
                  reservation.phoneNumber,
                  style: GoogleFonts.poppins(
                    color: Colors.grey.shade300,
                    fontSize: 14,
                  ),
                ),
              ],
            ),
          ],
          if (reservation.specialNotes?.isNotEmpty ?? false) ...[
            const SizedBox(height: 8),
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: Colors.amber.shade900.withOpacity(0.2),
                borderRadius: BorderRadius.circular(6),
                border: Border.all(color: Colors.amber.shade700),
              ),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Icon(Icons.note, color: Colors.amber.shade400, size: 16),
                  const SizedBox(width: 6),
                  Expanded(
                    child: Text(
                      reservation.specialNotes!,
                      style: GoogleFonts.poppins(
                        color: Colors.amber.shade200,
                        fontSize: 13,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
          const SizedBox(height: 8),
          Row(
            children: [
              Text(
                'Code: ${reservation.confirmationCode}',
                style: GoogleFonts.poppins(
                  color: Colors.blue.shade300,
                  fontSize: 12,
                  fontWeight: FontWeight.w500,
                ),
              ),
              const Spacer(),
              Text(
                '${reservation.durationMinutes} min',
                style: GoogleFonts.poppins(
                  color: Colors.grey.shade400,
                  fontSize: 12,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
