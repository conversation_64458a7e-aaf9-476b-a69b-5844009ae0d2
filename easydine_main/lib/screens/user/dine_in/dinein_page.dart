import 'dart:ui';

import 'package:animate_do/animate_do.dart';
import 'package:easydine_main/blocs/table/table_bloc.dart';
import 'package:easydine_main/models/table_model.dart';
import 'package:easydine_main/screens/user/dine_in/widgets/enhanced_stats_bar.dart';
import 'package:easydine_main/screens/user/dine_in/widgets/floor_indicator.dart';
import 'package:easydine_main/screens/user/dine_in/widgets/quick_action_card.dart';
import 'package:easydine_main/screens/user/dine_in/widgets/stat_item.dart';
import 'package:easydine_main/screens/user/dine_in/widgets/table_card.dart';
import 'package:easydine_main/services/table_service.dart';
import 'package:easydine_main/widgets/app_bar.dart';
import 'package:easydine_main/widgets/tiled_background.dart';
import 'package:easydine_main/widgets/reservations_drawer.dart';
import 'package:easydine_main/widgets/table_management_dialog.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_staggered_grid_view/flutter_staggered_grid_view.dart';
import 'package:go_router/go_router.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:intl/intl.dart';

import '../../../blocs/table/table_event.dart';
import '../../../router/router_constants.dart';
import '../../../utils/get_table_color.dart';
import '../../../utils/seat_painter.dart';
import '../../../utils/table_border_painter.dart';
import 'dialogs/handle_take_order.dart';
import 'dialogs/handle_view_bill.dart';

class DineInPage extends StatefulWidget {
  const DineInPage({super.key});

  @override
  State<DineInPage> createState() => _DineInPageState();
}

class _DineInPageState extends State<DineInPage> {
  final GlobalKey<ScaffoldState> _scaffoldKey = GlobalKey<ScaffoldState>();
  final PageController _floorController = PageController();
  int _currentFloor = 1;
  String? filterStatus;
  int? filterSeats;
  List<Map<String, dynamic>> allTables = [];
  List<Map<String, dynamic>> filteredTables = [];

  void resetFilters() {
    setState(() {
      filterStatus = null;
      filterSeats = null;
      filteredTables = List.from(allTables);
    });
  }

  @override
  void initState() {
    super.initState();
    _floorController.addListener(_handleFloorScroll);
    // Fetch floors and tables from bloc when page initializes
    context.read<TableBloc>().add(LoadFloorsAndTables());
    // Listen to bloc state changes
    context.read<TableBloc>().stream.listen((state) {
      setState(() {
        // Convert TableModel to Map<String, dynamic>
        allTables = state.tables
            .map((table) => {
                  'id': table
                      .tableIdString, // Use string ID for API compatibility
                  'intId': table.id, // Keep integer ID for legacy compatibility
                  'seats': table.seats,
                  'status': table.status,
                  'cleaningStatus': table.cleaningStatus,
                  'crossAxisCellCount': table.crossAxisCellCount,
                  'mainAxisCellCount': table.mainAxisCellCount,
                  'location': table.location,
                  'price': table.price,
                  'minimumSpend': table.minimumSpend,
                  'reservationFee': table.reservationFee,
                  'features': table.features,
                  'reservationTime': table.reservationTime,
                  'reservedBy': table.reservedBy,
                  'reservationDetails': table.reservationDetails,
                  'lastCleaned': table.lastCleaned,
                  'lastOccupied': table.lastOccupied,
                  'averageOccupancyTime': table.averageOccupancyTime,
                  'popularitySco re': table.popularityScore,
                  'section': table.section,
                  'tableNumber': table.tableNumber,
                  'floor': table.floor,
                  'bookedSeats': table.bookedSeats,
                  'isAvailable': table.isAvailable,
                  'isOccupied': table.isOccupied,
                  'isReserved': table.isReserved,
                  'needsCleaning': table.needsCleaning,
                })
            .toList();
        filteredTables = List.from(allTables);

        // Update current floor from state
        if (state.currentFloorId != null) {
          _currentFloor = int.tryParse(state.currentFloorId!) ?? 1;
        }
      });
    });
  }

  @override
  void dispose() {
    _floorController.removeListener(_handleFloorScroll);
    _floorController.dispose();
    super.dispose();
  }

  void _handleFloorScroll() {
    if (_floorController.page != null) {
      setState(() {
        _currentFloor = _floorController.page!.round() + 1;
        applyFilters(); // Reapply filters when floor changes
      });
    }
  }

  // Get unique floors from allTables
  List<int> get availableFloors {
    return allTables.map((table) => table['floor'] as int).toSet().toList()
      ..sort();
  }

  // Get tables for current floor
  List<Map<String, dynamic>> get currentFloorTables {
    return filteredTables
        .where((table) => table['floor'] == _currentFloor)
        .toList();
  }

  // Modified applyFilters to include floor filtering
  void applyFilters() {
    setState(() {
      filteredTables = allTables.where((table) {
        bool matchesStatus =
            filterStatus == null || table['status'] == filterStatus;
        bool matchesSeats =
            filterSeats == null || table['seats'] == filterSeats;
        return matchesStatus && matchesSeats;
      }).toList();
    });
  }

  void _handleTableStatusChange(
      BuildContext context, Map<String, dynamic> table) {
    setState(() {
      final index = allTables.indexWhere((t) => t['id'] == table['id']);
      if (index != -1) {
        if (table['status'] == 'Available') {
          allTables[index]['status'] = 'Occupied';
        } else if (table['status'] == 'Occupied') {
          allTables[index]['status'] = 'Available';
        }
        filteredTables = List.from(allTables);
      }
    });
  }

  void _showTableDetails(BuildContext context, Map<String, dynamic> table) {
    final primaryGreen = Color(0xFF2CBF5A);
    final accentBlue = Color(0xFF2196F3);
    final warningAmber = Color(0xFFFFA000);
    final errorRed = Color(0xFFE53935);

    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      isScrollControlled: true,
      builder: (context) => BackdropFilter(
        filter: ImageFilter.blur(sigmaX: 5, sigmaY: 5),
        child: Container(
          height: MediaQuery.of(context).orientation == Orientation.landscape
              ? MediaQuery.of(context).size.height * 0.9
              : MediaQuery.of(context).size.height * 0.8,
          decoration: BoxDecoration(
            color: Colors.grey[900],
            borderRadius: BorderRadius.vertical(top: Radius.circular(24)),
          ),
          child: Padding(
            padding: const EdgeInsets.all(24.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Header - Fixed size container
                Container(
                  padding: EdgeInsets.all(16),
                  height: MediaQuery.of(context).orientation ==
                          Orientation.landscape
                      ? MediaQuery.of(context).size.height * 0.25
                      : MediaQuery.of(context).size.height * 0.15,
                  decoration: BoxDecoration(
                    color: Colors.black26,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Row(
                    children: [
                      SizedBox(
                        width: MediaQuery.of(context).orientation ==
                                Orientation.landscape
                            ? MediaQuery.of(context).size.width * 0.2
                            : MediaQuery.of(context).size.width * 0.35,
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'Table ${table['tableNumber']}',
                              style: GoogleFonts.poppins(
                                color: Colors.white,
                                fontSize: 24,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            Expanded(
                              child: Container(
                                margin: EdgeInsets.only(top: 4),
                                child: Stack(
                                  fit: StackFit.expand,
                                  children: [
                                    CustomPaint(
                                      painter: SeatPainter(
                                        seatCount: table['seats'],
                                        color:
                                            Color.fromRGBO(207, 207, 207, 1.0),
                                      ),
                                      size: Size.infinite,
                                    ),
                                    CustomPaint(
                                      painter: TableBorderPainter(
                                        seats: table['seats'],
                                        color: getTableColor(table['status']),
                                        strokeWidth: 3.0,
                                      ),
                                      size: Size.infinite,
                                    ),
                                  ],
                                ),
                              ),
                            )
                          ],
                        ),
                      ),
                      Expanded(
                        flex: 2,
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            // Status label
                            Text(
                              "STATUS",
                              style: GoogleFonts.poppins(
                                color: Colors.white60,
                                fontSize: 12,
                                fontWeight: FontWeight.w500,
                                letterSpacing: 0.5,
                              ),
                            ),
                            SizedBox(height: 8),

                            Container(
                              padding: EdgeInsets.symmetric(
                                  horizontal: 12, vertical: 8),
                              decoration: BoxDecoration(
                                color: getTableColor(table['status'])
                                    .withValues(alpha: 0.15),
                                borderRadius: BorderRadius.circular(12),
                                border: Border.all(
                                  color: getTableColor(table['status'])
                                      .withValues(alpha: 0.3),
                                  width: 1,
                                ),
                              ),
                              child: Row(
                                mainAxisSize: MainAxisSize.max, // Important!
                                children: [
                                  Container(
                                    width: 8,
                                    height: 8,
                                    decoration: BoxDecoration(
                                      color: getTableColor(table['status']),
                                      shape: BoxShape.circle,
                                    ),
                                  ),
                                  SizedBox(width: 8),
                                  Text(
                                    table['status'],
                                    style: GoogleFonts.poppins(
                                      color: getTableColor(table['status']),
                                      fontWeight: FontWeight.w600,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                            // Cleaning status label
                            Text(
                              "CLEANING",
                              style: GoogleFonts.poppins(
                                color: Colors.white60,
                                fontSize: 12,
                                fontWeight: FontWeight.w500,
                                letterSpacing: 0.5,
                              ),
                            ),
                            SizedBox(height: 8),

                            // Cleaning status - Fixed width
                            Container(
                              padding: EdgeInsets.symmetric(
                                  horizontal: 12, vertical: 8),
                              decoration: BoxDecoration(
                                color: getCleaningStatusColor(
                                        table['cleaningStatus'])
                                    .withValues(alpha: 0.15),
                                borderRadius: BorderRadius.circular(12),
                                border: Border.all(
                                  color: getCleaningStatusColor(
                                          table['cleaningStatus'])
                                      .withValues(alpha: 0.3),
                                  width: 1,
                                ),
                              ),
                              child: Row(
                                mainAxisSize: MainAxisSize.max, // Important!
                                children: [
                                  Container(
                                    width: 8,
                                    height: 8,
                                    decoration: BoxDecoration(
                                      color: getCleaningStatusColor(
                                          table['cleaningStatus']),
                                      shape: BoxShape.circle,
                                    ),
                                  ),
                                  SizedBox(width: 8),
                                  Text(
                                    table['cleaningStatus'],
                                    style: GoogleFonts.poppins(
                                      color: getCleaningStatusColor(
                                          table['cleaningStatus']),
                                      fontWeight: FontWeight.w600,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
                SizedBox(height: MediaQuery.of(context).size.height * 0.01),
                Expanded(
                  child: SingleChildScrollView(
                    physics: BouncingScrollPhysics(),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        FadeInUp(
                          duration: Duration(milliseconds: 400),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                'Table Information',
                                style: GoogleFonts.poppins(
                                  color: Colors.white70,
                                  fontSize: 16,
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                              SizedBox(
                                  height: MediaQuery.of(context).size.height *
                                      0.01),
                              Row(
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceAround,
                                children: [
                                  buildStatItem(
                                    icon: Icons.event_seat,
                                    label: 'Capacity',
                                    value: '${table['seats']} seats',
                                    color: warningAmber,
                                  ),
                                  buildStatItem(
                                    icon: Icons.location_on,
                                    label: 'Location',
                                    value: table['location'],
                                    color: accentBlue,
                                  ),
                                  if (table['status'] == 'Reserved')
                                    buildStatItem(
                                      icon: Icons.access_time,
                                      label: 'Reserved For',
                                      value: table['reservationTime'] != null
                                          ? DateFormat('dd-mm-yy hh:mm a')
                                              .format(table['reservationTime'])
                                          : 'N/A',
                                      color: primaryGreen,
                                    ),
                                ],
                              ),
                            ],
                          ),
                        ),
                        SizedBox(
                            height: MediaQuery.of(context).size.height * 0.01),
                        FadeInUp(
                          duration: Duration(milliseconds: 600),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                'Quick Actions',
                                style: GoogleFonts.poppins(
                                  color: Colors.white70,
                                  fontSize: 16,
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                              SizedBox(height: 16),
                              GridView.count(
                                shrinkWrap: true,
                                // Remove NeverScrollableScrollPhysics to enable scrolling within the parent SingleChildScrollView
                                physics: NeverScrollableScrollPhysics(),
                                crossAxisCount:
                                    MediaQuery.of(context).orientation ==
                                            Orientation.landscape
                                        ? 3
                                        : 2,
                                mainAxisSpacing: 8,
                                crossAxisSpacing: 8,
                                childAspectRatio: 2.5,
                                children: [
                                  buildQuickActionCard(
                                    icon: Icons.restaurant_menu,
                                    label: 'Take Order',
                                    color: primaryGreen,
                                    onTap: () =>
                                        handleTakeOrder(context, table),
                                  ),
                                  buildQuickActionCard(
                                    icon: Icons.receipt_long,
                                    label: 'View Bill',
                                    color: accentBlue,
                                    onTap: () => handleViewBill(context, table),
                                  ),
                                  buildQuickActionCard(
                                    icon: Icons.cleaning_services,
                                    label: 'Mark Clean',
                                    color: warningAmber,
                                    onTap: () =>
                                        _handleMarkClean(context, table),
                                  ),
                                  buildQuickActionCard(
                                    icon: Icons.event_available,
                                    label: 'Reserve',
                                    color: Colors.purple,
                                    onTap: () {
                                      if (table['status'] != 'Available') {
                                        showDialog(
                                          context: context,
                                          builder: (context) => AlertDialog(
                                            backgroundColor: Colors.grey[900],
                                            title: Text(
                                              'Table Not Available',
                                              style: GoogleFonts.poppins(
                                                  color: Colors.white),
                                            ),
                                            content: Text(
                                              'Only available tables can be reserved.',
                                              style: GoogleFonts.poppins(
                                                  color: Colors.white70),
                                            ),
                                            actions: [
                                              TextButton(
                                                onPressed: () =>
                                                    Navigator.pop(context),
                                                child: Text('OK'),
                                              ),
                                            ],
                                          ),
                                        );
                                        return;
                                      }
                                      Navigator.pop(
                                          context); // Close details dialog
                                      _showReservationDialog(context, table);
                                    },
                                  ),
                                  buildQuickActionCard(
                                    icon: Icons.settings,
                                    label: 'Manage',
                                    color: Colors.grey,
                                    onTap: () {
                                      Navigator.pop(
                                          context); // Close details dialog
                                      showDialog(
                                        context: context,
                                        builder: (context) =>
                                            TableManagementDialog(table: table),
                                      );
                                    },
                                  ),
                                ],
                              ),
                            ],
                          ),
                        ),
                        if (table['status'] == 'Reserved') ...[
                          SizedBox(height: 24),
                          FadeInUp(
                            duration: Duration(milliseconds: 700),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  'Reservation Details',
                                  style: GoogleFonts.poppins(
                                    color: Colors.white70,
                                    fontSize: 16,
                                    fontWeight: FontWeight.w600,
                                  ),
                                ),
                                SizedBox(height: 16),
                                ListTile(
                                  leading: Icon(Icons.person_outline,
                                      color: Colors.purple),
                                  title: Text(
                                    table['reservationDetails']['name'],
                                    style: TextStyle(color: Colors.white),
                                  ),
                                  subtitle: Text(
                                    table['reservationDetails']['phone'],
                                    style: TextStyle(color: Colors.white70),
                                  ),
                                ),
                                ListTile(
                                  leading: Icon(Icons.access_time,
                                      color: Colors.purple),
                                  title: Text(
                                    'Reserved for ${table['reservationDetails']['time']}',
                                    style: TextStyle(color: Colors.white),
                                  ),
                                  subtitle: Text(
                                    '${table['reservationDetails']['guests']} guests',
                                    style: TextStyle(color: Colors.white70),
                                  ),
                                ),
                                if (table['reservationDetails']['notes']
                                        ?.isNotEmpty ??
                                    false)
                                  ListTile(
                                    leading: Icon(Icons.note_outlined,
                                        color: Colors.purple),
                                    title: Text(
                                      'Notes',
                                      style: TextStyle(color: Colors.white),
                                    ),
                                    subtitle: Text(
                                      table['reservationDetails']['notes'],
                                      style: TextStyle(color: Colors.white70),
                                    ),
                                  ),
                              ],
                            ),
                          ),
                        ],
                        SizedBox(height: 80),
                      ],
                    ),
                  ),
                ),

                // Action Button - Fixed at bottom
                FadeInUp(
                  duration: Duration(milliseconds: 800),
                  child: Container(
                    margin: EdgeInsets.only(top: 16),
                    child: Row(
                      children: [
                        Expanded(
                          child: ElevatedButton(
                            style: ElevatedButton.styleFrom(
                              backgroundColor: table['status'] == 'Available'
                                  ? primaryGreen
                                  : errorRed,
                              padding: EdgeInsets.symmetric(vertical: 16),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(12),
                              ),
                            ),
                            onPressed: () =>
                                _handleTableStatusChange(context, table),
                            child: Text(
                              table['status'] == 'Available'
                                  ? 'Mark as Occupied'
                                  : 'Close Table',
                              style: GoogleFonts.poppins(
                                color: Colors.white,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  void _handleMarkClean(BuildContext context, Map<String, dynamic> table) {
    if (table['status'] == 'Occupied') {
      showDialog(
        context: context,
        builder: (context) => AlertDialog(
          backgroundColor: Colors.grey[900],
          title: Text(
            'Table Still Occupied',
            style: GoogleFonts.poppins(color: Colors.white),
          ),
          content: Text(
            'Please close the table before changing cleaning status.',
            style: GoogleFonts.poppins(color: Colors.white70),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: Text('OK'),
            ),
          ],
        ),
      );
      return;
    }

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: Colors.grey[900],
        title: Text(
          'Update Cleaning Status',
          style: GoogleFonts.poppins(color: Colors.white),
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            _buildCleaningStatusButton(
              context,
              table,
              'Clean',
              Color(0xFF2CBF5A),
            ),
            SizedBox(height: 8),
            _buildCleaningStatusButton(
              context,
              table,
              'Needs Cleaning',
              Color(0xFFFFA000),
            ),
            SizedBox(height: 8),
            _buildCleaningStatusButton(
              context,
              table,
              'Dirty',
              Color(0xFFE53E3E),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCleaningStatusButton(
    BuildContext context,
    Map<String, dynamic> table,
    String status,
    Color color,
  ) {
    return ElevatedButton(
      style: ElevatedButton.styleFrom(
        backgroundColor: color.withValues(alpha: 0.2),
        padding: EdgeInsets.symmetric(horizontal: 24, vertical: 12),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
          side: BorderSide(color: color.withValues(alpha: 0.5)),
        ),
      ),
      onPressed: () {
        _handleCleaningStatusChange(table, status);
        Navigator.pop(context);
        Navigator.pop(context);
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Table marked as $status'),
            backgroundColor: color,
            duration: Duration(seconds: 2),
          ),
        );
      },
      child: Text(
        'Mark as $status',
        style: GoogleFonts.poppins(
          color: Colors.white,
          fontWeight: FontWeight.w600,
        ),
      ),
    );
  }

  void _handleCleaningStatusChange(
      Map<String, dynamic> table, String newStatus) {
    // Use the new API to update table cleaning status
    final tableBloc = context.read<TableBloc>();
    tableBloc.add(UpdateTableCleaningStatus(
      tableId: table['id'],
      newStatus: newStatus,
    ));
  }

  void _showReservationDialog(
      BuildContext context, Map<String, dynamic> table) {
    final dateTimeController = TextEditingController();
    final nameController = TextEditingController();
    final phoneController = TextEditingController();
    final guestsController = TextEditingController();
    final notesController = TextEditingController();

    DateTime? selectedDateTime;

    // Modern color palette
    final backgroundColor = Color(0xFF1A1A1A);
    final surfaceColor = Color(0xFF2D2D2D);
    final primaryColor = Color(0xFF2CBF5A); // Modern green

    final textColor = Colors.white;
    final subtextColor = Colors.white70;

    showDialog(
      context: context,
      builder: (context) => Dialog(
        backgroundColor: Colors.transparent,
        child: Container(
          width: 400,
          decoration: BoxDecoration(
            color: backgroundColor,
            borderRadius: BorderRadius.circular(16),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.2),
                blurRadius: 20,
                offset: Offset(0, 10),
              ),
            ],
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Header
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: surfaceColor,
                  borderRadius:
                      const BorderRadius.vertical(top: Radius.circular(16)),
                ),
                child: Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: primaryColor.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Icon(
                        Icons.event_available,
                        color: primaryColor,
                        size: 24,
                      ),
                    ),
                    const SizedBox(width: 16),
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Reserve Table ${table['tableNumber']}',
                          style: GoogleFonts.poppins(
                            color: textColor,
                            fontSize: 20,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                        Text(
                          '${table['seats']} seats available',
                          style: GoogleFonts.poppins(
                            color: subtextColor,
                            fontSize: 14,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),

              // Form Content
              Padding(
                padding:
                    const EdgeInsets.symmetric(horizontal: 12, vertical: 12),
                child: SingleChildScrollView(
                  child: Column(
                    children: [
                      _buildTextField(
                        controller: nameController,
                        label: 'Customer Name',
                        icon: Icons.person_outline,
                        primaryColor: primaryColor,
                        backgroundColor: backgroundColor,
                        surfaceColor: surfaceColor,
                      ),
                      SizedBox(
                          height: MediaQuery.of(context).size.height * 0.01),
                      _buildTextField(
                        controller: phoneController,
                        label: 'Phone Number',
                        icon: Icons.phone_outlined,
                        keyboardType: TextInputType.phone,
                        primaryColor: primaryColor,
                        backgroundColor: backgroundColor,
                        surfaceColor: surfaceColor,
                      ),
                      SizedBox(
                          height: MediaQuery.of(context).size.height * 0.01),
                      InkWell(
                        onTap: () async {
                          // Store context before async operations
                          final currentContext = context;

                          // Show date picker first
                          final DateTime? selectedDate = await showDatePicker(
                            context: currentContext,
                            initialDate: DateTime.now(),
                            firstDate: DateTime.now(),
                            lastDate:
                                DateTime.now().add(const Duration(days: 365)),
                            builder: (context, child) {
                              return Theme(
                                data: Theme.of(context).copyWith(
                                  datePickerTheme: DatePickerThemeData(
                                    backgroundColor: Colors.white,
                                    dayStyle: GoogleFonts.poppins(
                                      color: Colors.black,
                                    ),
                                    headerBackgroundColor: primaryColor,
                                    headerForegroundColor: Colors.white,
                                    dayForegroundColor:
                                        WidgetStateProperty.resolveWith(
                                            (states) {
                                      if (states
                                          .contains(WidgetState.selected)) {
                                        return Colors.white;
                                      }
                                      return Colors.black;
                                    }),
                                    dayBackgroundColor:
                                        WidgetStateProperty.resolveWith(
                                            (states) {
                                      if (states
                                          .contains(WidgetState.selected)) {
                                        return primaryColor;
                                      }
                                      return Colors.transparent;
                                    }),
                                    todayForegroundColor:
                                        WidgetStateProperty.all(primaryColor),
                                    todayBackgroundColor:
                                        WidgetStateProperty.all(
                                            Colors.transparent),
                                    yearForegroundColor:
                                        WidgetStateProperty.resolveWith(
                                            (states) {
                                      if (states
                                          .contains(WidgetState.selected)) {
                                        return Colors.white;
                                      }
                                      return textColor;
                                    }),
                                    yearBackgroundColor:
                                        WidgetStateProperty.resolveWith(
                                            (states) {
                                      if (states
                                          .contains(WidgetState.selected)) {
                                        return primaryColor;
                                      }
                                      return Colors.transparent;
                                    }),
                                    surfaceTintColor: Colors.transparent,
                                  ),
                                  textButtonTheme: TextButtonThemeData(
                                    style: TextButton.styleFrom(
                                      foregroundColor: primaryColor,
                                    ),
                                  ),
                                ),
                                child: child!,
                              );
                            },
                          );

                          if (selectedDate != null) {
                            // Show time picker after date is selected
                            final TimeOfDay? selectedTime =
                                await showTimePicker(
                              context: currentContext,
                              initialTime: TimeOfDay.now(),
                              builder: (context, child) {
                                return Theme(
                                  data: Theme.of(context).copyWith(
                                    timePickerTheme: TimePickerThemeData(
                                      backgroundColor: Color(0xFF1A1A1A),
                                      hourMinuteTextColor: Colors.white,
                                      dayPeriodTextColor: Colors.white,
                                      dialHandColor: primaryColor,
                                      dialBackgroundColor: Colors.grey[800]!,
                                      dialTextColor: textColor,
                                      entryModeIconColor: textColor,
                                      helpTextStyle:
                                          GoogleFonts.poppins(color: textColor),
                                      hourMinuteTextStyle: GoogleFonts.poppins(
                                        color: textColor,
                                        fontSize: 32,
                                        fontWeight: FontWeight.bold,
                                      ),
                                      dayPeriodTextStyle: GoogleFonts.poppins(
                                        color: textColor,
                                        fontSize: 16,
                                      ),
                                    ),
                                    textButtonTheme: TextButtonThemeData(
                                      style: TextButton.styleFrom(
                                        foregroundColor: Colors.green,
                                      ),
                                    ),
                                  ),
                                  child: child!,
                                );
                              },
                            );

                            if (selectedTime != null) {
                              selectedDateTime = DateTime(
                                selectedDate.year,
                                selectedDate.month,
                                selectedDate.day,
                                selectedTime.hour,
                                selectedTime.minute,
                              );

                              // Format the display text
                              final formattedDate = DateFormat('MMM dd, yyyy')
                                  .format(selectedDateTime!);
                              final formattedTime =
                                  selectedTime.format(currentContext);
                              dateTimeController.text =
                                  '$formattedDate at $formattedTime';
                            }
                          }
                        },
                        child: IgnorePointer(
                          child: _buildTextField(
                            controller: dateTimeController,
                            label: 'Reservation Date & Time',
                            icon: Icons.calendar_today,
                            primaryColor: primaryColor,
                            backgroundColor: backgroundColor,
                            surfaceColor: surfaceColor,
                            suffix: Icon(
                              Icons.arrow_drop_down,
                              color: primaryColor,
                            ),
                          ),
                        ),
                      ),
                      SizedBox(
                          height: MediaQuery.of(context).size.height * 0.01),
                      _buildTextField(
                        controller: guestsController,
                        label: 'Number of Guests',
                        icon: Icons.group_outlined,
                        keyboardType: TextInputType.number,
                        primaryColor: primaryColor,
                        backgroundColor: backgroundColor,
                        surfaceColor: surfaceColor,
                      ),
                      SizedBox(
                          height: MediaQuery.of(context).size.height * 0.01),
                      _buildTextField(
                        controller: notesController,
                        label: 'Special Notes (Optional)',
                        icon: Icons.note_outlined,
                        maxLines: 3,
                        primaryColor: primaryColor,
                        backgroundColor: backgroundColor,
                        surfaceColor: surfaceColor,
                      ),
                    ],
                  ),
                ),
              ),

              // Actions
              Container(
                padding: const EdgeInsets.all(24),
                decoration: BoxDecoration(
                  color: surfaceColor,
                  borderRadius:
                      const BorderRadius.vertical(bottom: Radius.circular(16)),
                ),
                child: Row(
                  children: [
                    Expanded(
                      child: TextButton(
                        onPressed: () => Navigator.pop(context),
                        style: TextButton.styleFrom(
                          padding: const EdgeInsets.symmetric(vertical: 16),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                        ),
                        child: Text(
                          'Cancel',
                          style: GoogleFonts.poppins(
                            color: subtextColor,
                            fontWeight: FontWeight.w500,
                            fontSize: 15,
                          ),
                        ),
                      ),
                    ),
                    SizedBox(height: MediaQuery.of(context).size.height * 0.01),
                    Expanded(
                      child: ElevatedButton(
                        onPressed: () async {
                          // Store context before async operations
                          final currentContext = context;

                          if (nameController.text.isEmpty ||
                              selectedDateTime == null ||
                              phoneController.text.isEmpty ||
                              guestsController.text.isEmpty) {
                            ScaffoldMessenger.of(context).showSnackBar(
                              SnackBar(
                                content: Text(
                                  'Please fill in all required fields',
                                  style: GoogleFonts.poppins(),
                                ),
                                backgroundColor: Colors.red.shade400,
                                behavior: SnackBarBehavior.floating,
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(10),
                                ),
                              ),
                            );
                            return;
                          }

                          final guests =
                              int.tryParse(guestsController.text) ?? 1;

                          // Show loading indicator
                          showDialog(
                            context: context,
                            barrierDismissible: false,
                            builder: (context) => Center(
                              child: CircularProgressIndicator(
                                color: primaryColor,
                              ),
                            ),
                          );

                          try {
                            // Use the table service to create reservation
                            final success =
                                await TableService.createTableReservation(
                              tableId: table['id'] as String,
                              customerName: nameController.text,
                              phoneNumber: phoneController.text,
                              numberOfGuests: guests,
                              reservationTime: selectedDateTime!,
                              specialNotes: notesController.text.isNotEmpty
                                  ? notesController.text
                                  : null,
                            );

                            // Close loading dialog
                            if (mounted) Navigator.pop(currentContext);
                            // Close reservation dialog
                            if (mounted) Navigator.pop(currentContext);

                            if (success) {
                              // Update table status via bloc
                              if (mounted) {
                                final tableBloc =
                                    currentContext.read<TableBloc>();
                                tableBloc.add(ReserveTable(
                                  tableId: table['id'] as String,
                                  bookedSeats: guests,
                                  reservationDetails: {
                                    'name': nameController.text,
                                    'phone': phoneController.text,
                                    'time': dateTimeController.text,
                                    'guests': guestsController.text,
                                    'notes': notesController.text,
                                  },
                                ));

                                ScaffoldMessenger.of(currentContext)
                                    .showSnackBar(
                                  SnackBar(
                                    content: Text(
                                      'Table ${table['tableNumber']} reserved successfully for ${nameController.text}',
                                      style: GoogleFonts.poppins(),
                                    ),
                                    backgroundColor: primaryColor,
                                    behavior: SnackBarBehavior.floating,
                                    shape: RoundedRectangleBorder(
                                      borderRadius: BorderRadius.circular(10),
                                    ),
                                  ),
                                );
                              }
                            } else {
                              if (mounted) {
                                ScaffoldMessenger.of(currentContext)
                                    .showSnackBar(
                                  SnackBar(
                                    content: Text(
                                      'Failed to create reservation. Please try again.',
                                      style: GoogleFonts.poppins(),
                                    ),
                                    backgroundColor: Colors.red.shade400,
                                    behavior: SnackBarBehavior.floating,
                                    shape: RoundedRectangleBorder(
                                      borderRadius: BorderRadius.circular(10),
                                    ),
                                  ),
                                );
                              }
                            }
                          } catch (e) {
                            // Close loading dialog
                            if (mounted) Navigator.pop(currentContext);

                            if (mounted) {
                              ScaffoldMessenger.of(currentContext).showSnackBar(
                                SnackBar(
                                  content: Text(
                                    'Error creating reservation: $e',
                                    style: GoogleFonts.poppins(),
                                  ),
                                  backgroundColor: Colors.red.shade400,
                                  behavior: SnackBarBehavior.floating,
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(10),
                                  ),
                                ),
                              );
                            }
                          }
                        },
                        style: ElevatedButton.styleFrom(
                          backgroundColor: primaryColor,
                          padding: const EdgeInsets.symmetric(vertical: 16),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                          elevation: 0,
                        ),
                        child: Text(
                          'Confirm Reservation',
                          style: GoogleFonts.poppins(
                            fontWeight: FontWeight.w500,
                            fontSize: 15,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildTextField({
    required TextEditingController controller,
    required String label,
    required IconData icon,
    required Color primaryColor,
    required Color backgroundColor,
    required Color surfaceColor,
    TextInputType? keyboardType,
    Widget? suffix,
    int? maxLines,
  }) {
    return Container(
      decoration: BoxDecoration(
        color: surfaceColor,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Colors.white.withValues(alpha: 0.1),
          width: 1,
        ),
      ),
      child: TextField(
        controller: controller,
        style: GoogleFonts.poppins(
          color: Colors.white,
          fontSize: 15,
        ),
        keyboardType: keyboardType,
        maxLines: maxLines ?? 1,
        decoration: InputDecoration(
          labelText: label,
          labelStyle: GoogleFonts.poppins(
            color: Colors.white.withValues(alpha: 0.6),
            fontSize: 14,
          ),
          prefixIcon: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: Icon(
              icon,
              color: primaryColor.withValues(alpha: 0.7),
              size: 22,
            ),
          ),
          suffixIcon: suffix,
          border: InputBorder.none,
          contentPadding:
              const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
        ),
      ),
    );
  }

  void _showFilterDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: Colors.grey[900],
        title: Text(
          'Filter Tables',
          style: GoogleFonts.poppins(color: Colors.white),
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Status filter
            DropdownButtonFormField<String>(
              value: filterStatus,
              dropdownColor: Colors.grey[850],
              style: TextStyle(color: Colors.white),
              decoration: InputDecoration(
                labelText: 'Status',
                labelStyle: TextStyle(color: Colors.white70),
                enabledBorder: UnderlineInputBorder(
                  borderSide: BorderSide(color: Colors.white30),
                ),
              ),
              items: [
                DropdownMenuItem(
                    value: null,
                    child: Text('All', style: TextStyle(color: Colors.white))),
                DropdownMenuItem(
                    value: 'Available',
                    child: Text('Available',
                        style: TextStyle(color: Colors.white))),
                DropdownMenuItem(
                    value: 'Occupied',
                    child: Text('Occupied',
                        style: TextStyle(color: Colors.white))),
                DropdownMenuItem(
                    value: 'Reserved',
                    child: Text('Reserved',
                        style: TextStyle(color: Colors.white))),
              ],
              onChanged: (value) {
                setState(() {
                  filterStatus = value;
                });
              },
            ),
            SizedBox(height: 16),
            // Seats filter
            DropdownButtonFormField<int>(
              value: filterSeats,
              dropdownColor: Colors.grey[850],
              style: TextStyle(color: Colors.white),
              decoration: InputDecoration(
                labelText: 'Number of Seats',
                labelStyle: TextStyle(color: Colors.white70),
                enabledBorder: UnderlineInputBorder(
                  borderSide: BorderSide(color: Colors.white30),
                ),
              ),
              items: [
                DropdownMenuItem(
                    value: null,
                    child: Text('All', style: TextStyle(color: Colors.white))),
                ...List.generate(8, (index) => index + 2)
                    .map((seats) => DropdownMenuItem(
                          value: seats,
                          child: Text('$seats seats',
                              style: TextStyle(color: Colors.white)),
                        ))
                    .toList(),
              ],
              onChanged: (value) {
                setState(() {
                  filterSeats = value;
                });
              },
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () {
              resetFilters();
              Navigator.pop(context);
            },
            child: Text('Reset'),
          ),
          ElevatedButton(
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.blue,
            ),
            onPressed: () {
              applyFilters();
              Navigator.pop(context);
            },
            child: Text('Apply'),
          ),
        ],
      ),
    );
  }

  Widget _buildTableLayout() {
    final floorTables = currentFloorTables;

    return Stack(
      children: floorTables.map((table) {
        final position = table['position'] as Offset?;
        final size = table['size'] as Size?;

        // If table doesn't have position/size, return regular grid view
        if (position == null || size == null) {
          return const SizedBox.shrink();
        }

        return Positioned(
          left: position.dx,
          top: position.dy,
          child: SizedBox(
            width: size.width,
            height: size.height,
            child: TableCard(
                table: TableModel(
                  id: table['intId'] as int,
                  apiTableId: table['id'] as String,
                  name: table['name'] ?? 'Unknown Table',
                  seats: table['seats'],
                  status: table['status'],
                  cleaningStatus: table['cleaningStatus'],
                  crossAxisCellCount: table['crossAxisCellCount'],
                  mainAxisCellCount: table['mainAxisCellCount'],
                  location: table['location'],
                  price: (table['price'] ?? 0).toDouble(),
                  minimumSpend: (table['minimumSpend'] ?? 0).toDouble(),
                  reservationFee: (table['reservationFee'] ?? 0).toDouble(),
                  features: List<String>.from(table['features'] ?? []),
                  reservationTime: table['reservationTime'],
                  reservedBy: table['reservedBy'],
                  reservationDetails: table['reservationDetails'],
                  lastCleaned: table['lastCleaned'],
                  lastOccupied: table['lastOccupied'],
                  averageOccupancyTime: table['averageOccupancyTime'],
                  popularityScore: (table['popularityScore'] ?? 0).toDouble(),
                  section: table['section'],
                  tableNumber: table['tableNumber'],
                  floor: table['floor'],
                  position: table['position'],
                  size: table['size'],
                  occupiedBy: table['occupiedBy'],
                ),
                onTap: () => _showTableDetails(context, table)),
          ),
        );
      }).toList(),
    );
  }

  @override
  Widget build(BuildContext context) {
    final isLandscape =
        MediaQuery.of(context).orientation == Orientation.landscape;

    return Scaffold(
      key: _scaffoldKey,
      extendBodyBehindAppBar: true,
      appBar: WaiterAppBar(scaffoldKey: _scaffoldKey),
      drawer: const ReservationsDrawer(),
      floatingActionButton: FloatingActionButton(
        onPressed: () {
          _scaffoldKey.currentState?.openDrawer();
        },
        backgroundColor: Colors.blue.shade600,
        child: const Icon(
          Icons.event_note,
          color: Colors.white,
        ),
      ),
      body: Stack(
        children: [
          const TiledBackground(),
          Column(
            children: [
              const SizedBox(height: kToolbarHeight + 20),
              buildEnhancedStatsBar(context, filteredTables),
              Expanded(
                child: PageView.builder(
                  controller: _floorController,
                  itemCount: availableFloors.length,
                  itemBuilder: (context, index) {
                    final floorTables = currentFloorTables;
                    return Column(
                      children: [
                        Padding(
                          padding: const EdgeInsets.symmetric(horizontal: 16),
                          child: Row(
                            children: [
                              Text(
                                'Floor ${index + 1}',
                                style: GoogleFonts.poppins(
                                  color: Colors.white,
                                  fontSize: 24,
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                              Spacer(),
                              FloorIndicator(
                                currentFloor: _currentFloor,
                                availableFloors: availableFloors,
                                onFloorChanged: (floor) {
                                  _floorController.animateToPage(
                                    floor - 1,
                                    duration: const Duration(milliseconds: 300),
                                    curve: Curves.easeInOut,
                                  );
                                },
                              ),
                              Spacer(),
                              // Add filter button
                              IconButton(
                                icon: Icon(Icons.filter_list,
                                    color: Colors.white70),
                                onPressed: _showFilterDialog,
                              ),
                              // Add layout manager button
                              IconButton(
                                icon:
                                    Icon(Icons.grid_on, color: Colors.white70),
                                onPressed: () async {
                                  final result =
                                      await GoRouter.of(context).pushNamed(
                                    RouterConstants.tableLayoutManager,
                                    extra: {
                                      'tables': allTables,
                                      'floor': _currentFloor,
                                    },
                                  );
                                  if (result != null) {
                                    setState(() {
                                      allTables =
                                          List<Map<String, dynamic>>.from(
                                              result as List);
                                      filteredTables = List.from(allTables);
                                    });
                                  }
                                },
                              ),
                              Text(
                                '${floorTables.length} Tables',
                                style: GoogleFonts.poppins(
                                  color: Colors.white70,
                                  fontSize: 16,
                                ),
                              ),
                            ],
                          ),
                        ),
                        const SizedBox(height: 16),
                        Expanded(
                          child: floorTables.isEmpty
                              ? Center(
                                  child: Text(
                                    'No tables on this floor',
                                    style: GoogleFonts.poppins(
                                      color: Colors.white60,
                                      fontSize: 16,
                                    ),
                                  ),
                                )
                              : _shouldUseLayoutView(floorTables)
                                  ? InteractiveViewer(
                                      boundaryMargin: EdgeInsets.all(20),
                                      minScale: 0.5,
                                      maxScale: 2.0,
                                      child: SizedBox(
                                        width:
                                            MediaQuery.of(context).size.width,
                                        height:
                                            MediaQuery.of(context).size.height *
                                                0.8,
                                        child: _buildTableLayout(),
                                      ),
                                    )
                                  : SingleChildScrollView(
                                      dragStartBehavior:
                                          DragStartBehavior.start,
                                      child: Padding(
                                        padding: const EdgeInsets.all(12.0),
                                        child: StaggeredGrid.count(
                                          // Adjust crossAxisCount based on orientation
                                          crossAxisCount: isLandscape ? 6 : 4,
                                          mainAxisSpacing: 16,
                                          crossAxisSpacing: 16,
                                          children: floorTables.map((table) {
                                            // Adjust cell counts based on orientation
                                            int crossAxisCount = isLandscape
                                                ? (table['crossAxisCellCount']
                                                    as int)
                                                : table['crossAxisCellCount']
                                                    as int;

                                            int mainAxisCount = isLandscape
                                                ? (table['mainAxisCellCount']
                                                    as int)
                                                : table['mainAxisCellCount']
                                                    as int;

                                            // Optional: Scale down sizes in landscape to fit more tables
                                            if (isLandscape) {
                                              crossAxisCount =
                                                  (crossAxisCount * 0.8).ceil();
                                              mainAxisCount =
                                                  (mainAxisCount * 0.8).ceil();
                                            }

                                            return StaggeredGridTile.count(
                                              crossAxisCellCount:
                                                  crossAxisCount,
                                              mainAxisCellCount: mainAxisCount,
                                              child: TableCard(
                                                table: TableModel(
                                                  id: table['intId'] as int,
                                                  apiTableId:
                                                      table['id'] as String,
                                                  name: table['name'] ??
                                                      'Unknown Table',
                                                  seats: table['seats'],
                                                  status: table['status'],
                                                  cleaningStatus:
                                                      table['cleaningStatus'],
                                                  crossAxisCellCount: table[
                                                      'crossAxisCellCount'],
                                                  mainAxisCellCount: table[
                                                      'mainAxisCellCount'],
                                                  location: table['location'],
                                                  price: table['price'] != null
                                                      ? (table['price'] as num)
                                                          .toDouble()
                                                      : 0.0,
                                                  minimumSpend: table[
                                                              'minimumSpend'] !=
                                                          null
                                                      ? (table['minimumSpend']
                                                              as num)
                                                          .toDouble()
                                                      : 0.0,
                                                  reservationFee: table[
                                                              'reservationFee'] !=
                                                          null
                                                      ? (table['reservationFee']
                                                              as num)
                                                          .toDouble()
                                                      : 0.0,
                                                  features: List<String>.from(
                                                      table['features'] ?? []),
                                                  reservationTime:
                                                      table['reservationTime'],
                                                  reservedBy:
                                                      table['reservedBy'],
                                                  reservationDetails: table[
                                                      'reservationDetails'],
                                                  lastCleaned:
                                                      table['lastCleaned'],
                                                  lastOccupied:
                                                      table['lastOccupied'],
                                                  averageOccupancyTime: table[
                                                          'averageOccupancyTime'] ??
                                                      0,
                                                  popularityScore: table[
                                                              'popularityScore'] !=
                                                          null
                                                      ? (table['popularityScore']
                                                              as num)
                                                          .toDouble()
                                                      : 0.0,
                                                  section: table['section'],
                                                  tableNumber:
                                                      table['tableNumber'],
                                                  floor: table['floor'],
                                                  position: table['position'],
                                                  size: table['size'],
                                                  occupiedBy:
                                                      table['occupiedBy'],
                                                ),
                                                onTap: () => _showTableDetails(
                                                    context, table),
                                              ),
                                            );
                                          }).toList(),
                                        ),
                                      ),
                                    ),
                        ),
                      ],
                    );
                  },
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  // Helper method to determine if we should use layout view
  bool _shouldUseLayoutView(List<Map<String, dynamic>> tables) {
    return tables
        .any((table) => table['position'] != null && table['size'] != null);
  }
}
